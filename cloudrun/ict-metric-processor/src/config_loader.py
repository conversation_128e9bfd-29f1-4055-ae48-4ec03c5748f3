"""
This module handles loading and managing metric configurations from Postgres and memory.
It provides functionality to load and manage configs per tenant/facility/fact_type.
"""

from threading import Lock
from typing import Dict

import structlog

from src.services.postgres_factory import PostgresFactory
from src.schemas import config_schema

logger = structlog.get_logger(__name__)


class ConfigLoader:
    """
    Handles loading and managing metric configurations.

    This class is responsible for:
    1. Loading configs from Postgres for each tenant/facility/fact_type
    2. Managing configs in memory
    3. Handling config validation

    This is implemented as a singleton to ensure config caching is shared across all requests
    within a CloudRun instance.
    """

    # =============== Singleton Implementation ===============
    _instance = None
    _lock = Lock()
    _initialized = False

    def __new__(cls):
        """Thread-safe singleton implementation."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        """Thread-safe initialization of the config loader."""
        if not self._initialized:
            with self.__class__._lock:  # Use class-level lock
                if not self._initialized:
                    # Structure: configs[tenant_id][facility_id][fact_type][metric_config_name]
                    # = config
                    self.configs = {}
                    self._initialized = True

    @classmethod
    def reset(cls) -> None:
        """
        Reset the singleton instance.
        This should only be used in testing or when absolutely necessary.
        """
        with cls._lock:
            cls._instance = None
            cls._initialized = False

    # =============== Public Interface ===============
    def get_config_by_fact_type(
        self, tenant: str, facility: str, fact_type: str
    ) -> Dict[str, config_schema.MetricConfig]:
        """
        Get metric configurations for a specific fact type, using in-memory cache if available.
        """
        # Check in-memory cache first at configs/tenant/facility/fact_type
        if (
            tenant in self.configs
            and facility in self.configs[tenant]
            and fact_type in self.configs[tenant][facility]
        ):
            return self.configs[tenant][facility][fact_type]

        # Not in cache: load from Postgres
        try:
            logger.debug(
                "Loading configs from Postgres",
                tenant=tenant,
                facility=facility,
                fact_type=fact_type,
            )

            postgres_service = PostgresFactory.get_instance(tenant)
            if not postgres_service:
                logger.error(
                    "Failed to get Postgres service",
                    tenant=tenant,
                )
                return {}

            # Get the metric configs from postgres as a list of dictionaries
            metric_config_rows = postgres_service.get_metric_configs(
                tenant=tenant,
                facility_id=facility,
                fact_type=fact_type,
            )

            # If no metric configs are found, return an empty dict
            if not metric_config_rows:
                logger.warning(
                    f"No metric configs found for fact type {fact_type}",
                    tenant=tenant,
                    facility=facility,
                    fact_type=fact_type,
                )
                return {}

            # Build the fact_type metrics dict
            fact_type_metrics_dict = {}
            for row in metric_config_rows:
                try:
                    # Get the config data from the row
                    config_data = row.get("config_data", row)
                    # Validate the config data against the pydantic schema
                    metric_config = config_schema.MetricConfig.model_validate(
                        config_data
                    )
                    # Set key of metric config to the metric config name
                    fact_type_metrics_dict[metric_config.metric_config_name] = (
                        metric_config
                    )
                except Exception:
                    logger.error(
                        "Error processing metric config row",
                        exc_info=True,
                        row=row,
                        tenant=tenant,
                        facility=facility,
                        fact_type=fact_type,
                    )
                    # Continue to the next row
                    continue

            # Store in cache
            self.configs.setdefault(tenant, {}).setdefault(facility, {})[
                fact_type
            ] = fact_type_metrics_dict

            return fact_type_metrics_dict

        except Exception:
            logger.error(
                "Error getting configs",
                exc_info=True,
                facility=facility,
                fact_type=fact_type,
                tenant=tenant,
            )
            raise

    def clear_cache(self) -> None:
        """
        Clear the entire config cache.
        This should be called when configs need to be reloaded.
        """
        self.configs = {}
        logger.info("Config cache cleared")
