import importlib
import json
import base64
import subprocess
import sys
import os
from dotenv import load_dotenv

load_dotenv()  # Load environment variables from .env.

if len(sys.argv) < 3:
    raise Exception("Command requires two arguments")


# Initialize tenant.
tenant_module = None
tenant_name = sys.argv[1]
area_node_name = sys.argv[2]

# Try to import mock data for given tenant_name.
try:
    tenant_module = importlib.import_module(
        f"data.{tenant_name}.{area_node_name}_node_data"
    )
except ImportError:
    # Display error message
    raise Exception(
        f"unable to import mock data for tenant: {tenant_name} and area node: {area_node_name}."
    )
except Exception as e:
    print(f"ERROR: {e}")


def handle_message(message):
    """Given a message, this method sends the array of fact data and attributes to the metric processor for processing.

    Args:
        message dictionary: (messageId: string, data: array of fact message data, attributes: dictionary of strings)
    """
    # Convert JSON to a string and then to bytes
    data_json_bytes = json.dumps(message["data"]).encode("utf-8")

    # # Encode the bytes to a base64 string
    data_base64_encoded = base64.b64encode(data_json_bytes).decode("utf-8")

    print(
        {
            "messageId": message["messageId"],
            "data": data_base64_encoded,
            "attributes": message["attributes"],
        }
    )

    port = os.getenv("PORT", "8080")

    curl_command = [
        "curl",
        "-X",
        "POST",
        f"http://localhost:{port}/",
        "-H",
        "Content-Type: application/json",
        "-d",
        json.dumps(
            {
                "message": {
                    "messageId": message["messageId"],
                    "data": data_base64_encoded,
                    "attributes": message["attributes"],
                },
            }
        ),
    ]

    # Execute the curl command using subprocess
    result = subprocess.run(curl_command, capture_output=True, text=True)

    # Print the output from curl
    print("Status:", result.returncode)
    print("Output:", result.stdout)
    if result.stderr:
        print("Error:", result.stderr)


# Loop through imported test data and handle each message batch.
for message in tenant_module.messages:
    handle_message(message)
