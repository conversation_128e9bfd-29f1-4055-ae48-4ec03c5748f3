from google.cloud import bigquery
from datetime import datetime
import base64, json, sys, os, copy
import google.cloud.logging

client = google.cloud.logging.Client()
client.setup_logging()
trace_context = ""

import logging

from table_map import tables
from tenant_map import tenants


def handler(request):
    """Responds to any HTTP request.
    Args:
        request (flask.Request): HTTP request object.
    Returns:
        The response text or any set of values that can be turned into a
        Response object using
        `make_response <http://flask.pocoo.org/docs/1.0/api/#flask.Flask.make_response>`.
    """
    request_json = request.get_json()
    trace_context = request.headers.get("x-cloud-trace-context")

    if request_json and "message" in request_json:
        logging.info(request_json)
        logging.info(f"x-cloud-trace-context: {trace_context}")
        process_facts(request_json)
        return "Success"
    else:
        logging.info("No Data")
        print("No Data")
        return "No Data", 422


project_id = os.environ.get("BIGQUERY_PROJECT", "ict-b-prototype-etlt")
dataset = "_landing"
bigquery_client = bigquery.Client(project=project_id)
datasets = list(bigquery_client.list_datasets())

default_tenant = os.environ.get("DEFAULT_TENANT", "ict_development")
default_facility = os.environ.get("DEFAULT_FACILITY", "ict_development")
default_source_system = os.environ.get("DEFAULT_SOURCE_SYSTEM", "ict_development")


def process_facts(cloud_event):

    # CloudRun is a persistant function so global variables can be accessed between connections
    local_tables = copy.deepcopy(tables)
    tenant_config = {}

    # The Pub/Sub message is passed as the CloudEvent's data payload.
    base64data = base64.b64decode(cloud_event["message"]["data"]).decode()
    attributes = cloud_event["message"].get("attributes", {})
    # If expected data not found, exit
    if not base64data:
        logging.error("No data found in message!")
        return "No data found in message!", 422

    logging.info(f"Processing messageId {cloud_event['message']['messageId']}")
    logging.info(attributes)

    if "tenant" in attributes:
        if attributes["tenant"] in tenants.keys():
            print(tenants[attributes["tenant"]])
            tenant_config = tenants[attributes["tenant"]]
        else:
            logging.error(f"Tenant {attributes['tenant']} not configured")
            return f"Tenant {attributes['tenant']} not configured", 422
    else:
        logging.error("No Tenant!")
        return "No Tenant!", 422

    logging.info("Tenant Config")
    logging.info(tenant_config)

    try:
        # Parse the event data
        fact_data = json.loads(base64data)
        logging.info(fact_data)
    except json.JSONDecodeError as err:
        logging.error("Error parsing JSON:", err)
        return "Error parsing JSON", 422

    # Alex's script sends with fact type in the individual data entries
    # DiQ has the fact type in attributes.type and data is the array of message

    # Iterate over each event in the file
    for fact_obj in fact_data:

        try:
            if fact_obj.get("value", "") != "":
                in_fact_type = fact_obj.get("type", "")
                insert_data = fact_obj.get("value", "")
                # logging.info(f"Data in values: Type: {in_fact_type}")
            else:
                in_fact_type = attributes.get("type", "")
                insert_data = fact_obj
                # logging.info(f"DiQ Data: Type: {in_fact_type}")
        except Exception as err:
            logging.error("Error parsing Data Object:", err)
            return "Error parsing Data Object", 422

        # DEBUG ONLY!
        # Setting the replace_time in the tenant config will overwrite the event time variables with the current datetime
        # to simulate current data
        # DRT should be responsible for this in the near future and this should be removed
        if tenant_config["replace_time"]:
            insert_data["eventTime"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        fact_type = (
            in_fact_type.lower()
        )  # Incoming fact type can come in various casing
        # Based on the type of fact, add a new row to the corresponding array
        if fact_type != "":
            if fact_type not in local_tables.keys():
                logging.warning(f"Fact Type not in known types: {in_fact_type}")
                local_tables["unknownfact"]["rows"].append(
                    {
                        "tenant": attributes.get("tenant", default_tenant),
                        "facility": attributes.get("facility", default_facility),
                        "source_system": attributes.get(
                            "sourcesystem",
                            attributes.get("source_system", default_source_system),
                        ),
                        "fact_type": in_fact_type,
                        "data": json.dumps(insert_data),
                        "ingestion_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    }
                )
            else:
                local_tables[fact_type]["rows"].append(
                    {
                        "tenant": attributes.get("tenant", default_tenant),
                        "facility": attributes.get("facility", default_facility),
                        "source_system": attributes.get(
                            "sourcesystem",
                            attributes.get("source_system", default_source_system),
                        ),
                        "data": json.dumps(insert_data),
                        "ingestion_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    }
                )
        else:
            logging.warning(f"Fact Type not in message")
            local_tables["unknownfact"]["rows"].append(
                {
                    "tenant": attributes.get("tenant", default_tenant),
                    "facility": attributes.get("facility", default_facility),
                    "source_system": attributes.get(
                        "sourcesystem",
                        attributes.get("source_system", default_source_system),
                    ),
                    "fact_type": "Undefined",
                    "data": json.dumps(fact_obj),
                    "ingestion_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                }
            )

    # For each table type, send the new rows to BigQuery if there are any
    for table, cur_table in local_tables.items():
        if cur_table["rows"]:
            insert_rows_as_stream(
                tenant_config["landing_dataset"], cur_table["table"], cur_table["rows"]
            )

    # For absolute certainty to avoid perstance of data
    del local_tables


def insert_rows_as_stream(dataset_id, table_id, rows):
    # Inserts the JSON objects into dataset:table_id.

    # TODO: Break data rows into smaller sets to avoid the 1M insert quota limit
    # 1000 rows?
    chunk = 1000

    # Insert data into a table
    if dataset_id in [dataset.dataset_id for dataset in datasets]:
        logging.info(f"Inserting {len(rows)} rows into {dataset_id}:{table_id}")
        for i in range(0, len(rows), chunk):
            result = bigquery_client.insert_rows_json(
                bigquery_client.dataset(dataset_id).table(table_id), rows[i : i + chunk]
            )
            logging.info(f"BigQuery Insert Response {i}: {result}")
    else:
        logging.info(f"Dataset {dataset_id} not found in project {project_id}")
